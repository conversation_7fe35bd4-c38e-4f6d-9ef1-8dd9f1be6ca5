import { envRequest } from '@/request';

// ===== 类型定义 =====

/**
 * 通用 API 响应接口
 */
interface ApiResponse<T = unknown> {
  code: number;
  data: T;
  message?: string;
  total?: number;
}

/**
 * 时间范围参数接口
 */
interface TimeRangeParams {
  startTm: string;
  endTm: string;
}

/**
 * 设备查询参数接口
 */
interface EquipQueryParams {
  equipId: string;
}

/**
 * 分页查询参数接口
 */
interface PageQueryParams {
  descs: string[];
  condition: Record<string, unknown>;
  currentPage: number;
  pageSize: number;
}

/**
 * 报告生成参数接口
 */
interface ReportGenerateParams {
  reportTime?: string;
  reportName?: string;
  startTm?: string;
  endTm?: string;
  [key: string]: string | number | undefined;
}

/**
 * 分析 API 接口定义
 */
interface AnalysisApiInterface {
  /** 查询空气监测信息 */
  queryAirMonitorInfo: (data: TimeRangeParams) => Promise<ApiResponse<Record<string, unknown>>>;
  /** 根据月份查询监测信息 */
  queryMonitorInfoByMonth: (data: TimeRangeParams) => Promise<ApiResponse<unknown[]>>;
  /** 查询监测指标信息 */
  queryMonitorIndexInfo: (data: EquipQueryParams) => Promise<ApiResponse<unknown>>;
  /** 查询监测指标统计图信息 */
  queryMonitorIndexChartInfo: (
    data: Record<string, string | number>,
  ) => Promise<ApiResponse<unknown>>;
  /** 生成分析报告 */
  generateAnalysisReport: (data: ReportGenerateParams) => Promise<ApiResponse<unknown>>;
  /** 下载分析报告 */
  downloadAnalysisReport: (data: Record<string, string | number>) => Promise<ApiResponse<unknown>>;
  /** 查询分析报告列表 */
  queryAnalysisReportList: (data: PageQueryParams) => Promise<ApiResponse<unknown[]>>;
  /** 删除分析报告 */
  deleteReport: (id: string) => Promise<ApiResponse<unknown>>;
}

/**
 * 数据分析模块 API
 */
const analysisApi: AnalysisApiInterface = {
  /** 查询空气监测信息 */
  queryAirMonitorInfo: (data: { startTm: string; endTm: string }) => {
    return envRequest.post('/staticAnalysis/queryAirMonitorInfo', { data });
  },
  /** 根据月份查询监测信息 */
  queryMonitorInfoByMonth: (data: { startTm: string; endTm: string }) => {
    return envRequest.post('/staticAnalysis/queryMonitorInfoByMonth', { data });
  },
  /** 查询监测指标信息 */
  queryMonitorIndexInfo: (data: { equipId: string }) => {
    return envRequest.post('/staticAnalysis/queryMonitorIndexInfo', { data });
  },
  /** 查询监测指标统计图信息 */
  queryMonitorIndexChartInfo: (data: Record<string, string | number>) => {
    return envRequest.post('/staticAnalysis/queryMonitorIndexChartInfo', { data });
  },
  /** 生成分析报告 */
  generateAnalysisReport: (data: Record<string, string | number>) => {
    return envRequest.post('/statisticsReport/createAnalysisReports', { data });
  },
  /** 下载分析报告 */
  downloadAnalysisReport: (data: Record<string, string | number>) => {
    return envRequest.post('/envQualityOnLineMonitor/downloadAnalysisReport', { data });
  },
  /** 查询分析报告列表 */
  queryAnalysisReportList: (data) => {
    return envRequest.post('/statisticsReport/page', { data });
  },
  /** 删除分析报告 */
  deleteReport: (id: string) => {
    return envRequest.delete(`/statisticsReport/removeById`, { params: { id } });
  },
};

export default analysisApi;
